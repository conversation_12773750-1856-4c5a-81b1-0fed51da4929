package routes

import (
	"context"
	"time"

	"github.com/SSShooter/mind-elixir-backend-go/models"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// @Summary getUserData
// @Schemes
// @Description getUserData
// @Tags user
// @Router /api/user [get]
func getUserData(userColl *mongo.Collection) func(ctx *gin.Context) {
	return func(c *gin.Context) {
		uuid := c.MustGet("uuid").(string)
		var result bson.M
		err := userColl.FindOne(
			context.TODO(),
			bson.D{{"uuid", uuid}},
		).Decode(&result)
		if err != nil {
			c.JSON(400, gin.H{"error": "no data"})
			return
		}
		c.JSON(200, gin.H{"data": result})
	}
}

// @Summary getUserProfile
// @Schemes
// @Description Get user profile information including social links
// @Tags user
// @Produce json
// @Success 200 {object} models.UserProfile
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Router /api/user/profile [get]
func getUserProfile(profileColl *mongo.Collection) func(ctx *gin.Context) {
	return func(c *gin.Context) {
		uuid := c.MustGet("uuid").(string)

		var profile models.UserProfile
		err := profileColl.FindOne(
			context.TODO(),
			bson.M{"uuid": uuid},
		).Decode(&profile)

		if err != nil {
			if err == mongo.ErrNoDocuments {
				// 如果没有找到档案，返回默认档案
				profile = models.UserProfile{
					UUID:        uuid,
					SocialLinks: []models.SocialLink{},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}
			} else {
				c.JSON(400, gin.H{"error": "Failed to get user profile"})
				return
			}
		}

		c.JSON(200, gin.H{"data": profile})
	}
}

// @Summary updateUserProfile
// @Schemes
// @Description Update user profile information including social links
// @Tags user
// @Accept json
// @Produce json
// @Param profile body models.UserProfile true "User profile data"
// @Success 200 {object} models.UserProfile
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Router /api/user/profile [put]
func updateUserProfile(profileColl *mongo.Collection) func(ctx *gin.Context) {
	return func(c *gin.Context) {
		uuid := c.MustGet("uuid").(string)

		var profileData models.UserProfile
		if err := c.ShouldBindJSON(&profileData); err != nil {
			c.JSON(400, gin.H{"error": "Invalid profile data: " + err.Error()})
			return
		}

		// Validate social links
		for i, link := range profileData.SocialLinks {
			if link.URL == "" {
				c.JSON(400, gin.H{"error": "Social link URL cannot be empty"})
				return
			}
			if link.Platform == "" {
				c.JSON(400, gin.H{"error": "Social link platform cannot be empty"})
				return
			}
			// For custom links, require a label
			if link.Platform == "custom" && link.Label == "" {
				c.JSON(400, gin.H{"error": "Custom links must have a label"})
				return
			}
			profileData.SocialLinks[i] = link
		}

		// Set required fields
		profileData.UUID = uuid
		profileData.UpdatedAt = time.Now()

		// Update or create user profile
		filter := bson.M{"uuid": uuid}

		// Prepare update data excluding CreatedAt to avoid conflicts
		updateData := bson.M{
			"uuid":        profileData.UUID,
			"updatedAt":   profileData.UpdatedAt,
			"displayName": profileData.DisplayName,
			"avatar":      profileData.Avatar,
			"bio":         profileData.Bio,
			"location":    profileData.Location,
			"website":     profileData.Website,
			"socialLinks": profileData.SocialLinks,
		}

		update := bson.M{
			"$set": updateData,
			"$setOnInsert": bson.M{
				"createdAt": time.Now(),
			},
		}

		opts := options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)
		var updatedProfile models.UserProfile
		err := profileColl.FindOneAndUpdate(
			context.TODO(),
			filter,
			update,
			opts,
		).Decode(&updatedProfile)

		if err != nil {
			c.JSON(400, gin.H{"error": "Failed to update user profile" + err.Error()})
			return
		}

		c.JSON(200, gin.H{"data": updatedProfile})
	}
}

// @Summary getPublicProfile
// @Schemes
// @Description Get public user profile by UUID
// @Tags public
// @Produce json
// @Param uuid query string true "User UUID (format: provider:loginId)"
// @Success 200 {object} models.UserProfile
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Router /api/public/profile [get]
func getPublicProfile(profileColl *mongo.Collection) func(ctx *gin.Context) {
	return func(c *gin.Context) {
		uuid := c.Query("uuid")

		if uuid == "" {
			c.JSON(400, gin.H{"error": "uuid is required"})
			return
		}

		var profile models.UserProfile
		err := profileColl.FindOne(
			context.TODO(),
			bson.M{"uuid": uuid},
		).Decode(&profile)

		if err != nil {
			if err == mongo.ErrNoDocuments {
				c.JSON(404, gin.H{"error": "Public profile not found"})
				return
			}
			c.JSON(400, gin.H{"error": "Failed to get public profile"})
			return
		}

		c.JSON(200, gin.H{"data": profile})
	}
}

func AddUserRoutes(rg *gin.RouterGroup, userColl *mongo.Collection, profileColl *mongo.Collection) {
	rg.GET("", getUserData(userColl))
	rg.GET("/profile", getUserProfile(profileColl))
	rg.PUT("/profile", updateUserProfile(profileColl))
}

func AddPublicProfileRoutes(rg *gin.RouterGroup, profileColl *mongo.Collection) {
	rg.GET("/profile", getPublicProfile(profileColl))
}
